<template>
  <div class="areaItem">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-text">加载中...</div>
    </div>

    <!-- 空状态 -->
    <div v-else-if="isEmpty" class="empty-container">
      <div class="empty-text">暂无分类数据</div>
    </div>

    <!-- 数据列表 -->
    <div
      v-else
      class="areaItem_item"
      v-for="item in secondList"
      :key="item.id"
      :style="{
        backgroundImage: `url(${item.img})`,
        backgroundRepeat: 'no-repeat',
        backgroundSize: 'cover',
      }"
    >
      <div class="title">{{ item.name }}</div>
      <div class="icon">
        <div
          class="icon_item"
          v-for="ele in item.list"
          :key="ele.id"
          @click="onSecondCategoryClick(ele)"
        >
          <img
            class="img"
            :src="ele.img"
            :alt="ele.name"
            loading="lazy"
          />
          <div class="name">{{ ele.name }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { getClassification } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { categoryPid } from '@utils/storage.js'

// 响应式数据
const secondList = ref([])
const loading = ref(false)

// 计算属性 - 检查是否有数据
const hasData = computed(() => secondList.value.length > 0)
const isEmpty = computed(() => !loading.value && secondList.value.length === 0)

// 路由相关
const route = useRoute()
const router = useRouter()

// 查询省分助农栏目
const fetchClassification = async (id) => {
  try {
    const [err, json] = await getClassification({
      bizCode: getBizCode('GOODS'),
      category_pid: id,
      page_no: 1,
      page_size: 500
    })

    if (err) {
      console.error('获取分类数据失败:', err.msg)
      return []
    }

    return json || []
  } catch (error) {
    console.error('获取分类数据失败:', error)
    return []
  }
}

// 初始化数据
const init = async () => {
  if (loading.value) return // 防止重复加载

  try {
    loading.value = true

    // 查询二级分类列表
    const secondCategories = await fetchClassification(categoryPid.get())

    if (!secondCategories || secondCategories.length === 0) {
      secondList.value = []
      return
    }

    // 排序二级分类
    const sortedSecondCategories = secondCategories.sort((a, b) => b.pos - a.pos)

    // 构建二级分类数据结构
    const transList = sortedSecondCategories.map(item => ({
      id: item.id,
      name: item.name,
      img: item.img,
      list: []
    }))

    secondList.value = transList

    // 并行获取所有三级分类数据，限制并发数量
    const batchSize = 3 // 限制并发数量，避免请求过多
    for (let i = 0; i < sortedSecondCategories.length; i += batchSize) {
      const batch = sortedSecondCategories.slice(i, i + batchSize)
      const batchPromises = batch.map(async (item, batchIndex) => {
        const actualIndex = i + batchIndex
        try {
          const thirdList = await fetchClassification(item.id)
          const sortedThirdList = thirdList.sort((a, b) => b.pos - a.pos)
          if (secondList.value[actualIndex]) {
            secondList.value[actualIndex].list = sortedThirdList
          }
        } catch (error) {
          console.error(`获取三级分类失败 (ID: ${item.id}):`, error)
          if (secondList.value[actualIndex]) {
            secondList.value[actualIndex].list = []
          }
        }
      })

      await Promise.allSettled(batchPromises)
    }
  } catch (error) {
    console.error('初始化数据失败:', error)
    showToast('初始化数据失败')
    secondList.value = []
  } finally {
    loading.value = false
  }
}

// 点击品类跳转
const onSecondCategoryClick = (item) => {
  const timestamp = Date.now() // 使用 Date.now() 更简洁
  router.push({
    path: `/goodslist/${item.id}`,
    query: {
      ...route.query,
      timestamp
    }
  })
}

// 组件挂载时初始化
onMounted(() => {
  // 设置分类PID
  if (route.query.category_pid) {
    categoryPid.set(route.query.category_pid)
  }
  init()
})

// 暴露给模板使用的数据和方法
defineExpose({
  secondList,
  loading,
  onSecondCategoryClick,
  init
})
</script>

<style lang='less' scoped>
.areaItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  // 加载状态样式
  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    .loading-text {
      font-size: 16px;
      color: #666;
    }
  }

  // 空状态样式
  .empty-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 0;

    .empty-text {
      font-size: 16px;
      color: #999;
    }
  }

  .areaItem_item {
    width: 341px;
    margin-top: 8px;

    .title {
      padding-top: 15px;
      padding-bottom: 16px;
      margin: 0 20px;
      font-size: 18px;
      color: #171e24;
      line-height: 18px;
      font-weight: 500;
    }

    .icon {
      display: flex;
      flex-direction: row;
      flex-wrap: wrap;
      padding: 0 35px;

      .icon_item {
        margin-right: 33px;
        margin-bottom: 16px;

        .img {
          width: 68px;
          height: 68px;
        }

        .name {
          font-size: 14px;
          color: #171e24;
          line-height: 14px;
          font-weight: 400;
          text-align: center;
        }
      }

      .icon_item:nth-child(3n) {
        margin-right: 0;
      }
    }
  }
}
</style>

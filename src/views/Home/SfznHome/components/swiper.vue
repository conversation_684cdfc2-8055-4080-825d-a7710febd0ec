<template>
  <!-- 轮播 -->
  <div class="swiper">
    <Swipe
      :autoplay="3000"
      indicator-color="white"
      v-if="swiperlist && swiperlist.length > 0"
    >
      <SwipeItem
        v-for="(item, index) in swiperlist"
        :key="index"
        @click="swiperclick(item.url, item.bannerChName)"
      >
        <img :src="item.imgUrl" alt="" data-name="jd-components-swiper" />
      </SwipeItem>
    </Swipe>
  </div>
</template>

<script>
import { Swipe, SwipeItem } from 'vant-by-yukapril'
import { getBannerInfo } from '@/api/interface/bannerIcon'
import { getBizCode } from '@/utils/curEnv'
import { closeToast, showLoadingToast, showToast } from 'vant'

export default {
  components: { Swipe, SwipeItem },
  data () {
    return {
      swiperlist: []
    }
  },
  created () {
    this.init()
  },
  methods: {
    async init () {
      const params = {
        showPage: '1',
        bizCode: getBizCode('GOODS')
      }
      showLoadingToast()
      const [err, json] = await getBannerInfo(params)
      closeToast()
      if (err) {
        showToast(err.msg)
        return
      }
      this.swiperlist = json
    },
    swiperclick (url, bannerChName) {
      if (url) {
        window.location.href = url
      }
    }
  }
}
</script>

<style lang='less' scoped>
.swiper {
  padding: 0 17px;

  // 轮播
  /deep/ .van-swipe-item {
    border-radius: 8px;

    img {
      width: 100%;
      border-radius: 8px;
    }
  }
}
</style>
